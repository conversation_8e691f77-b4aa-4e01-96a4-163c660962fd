/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* Container */
.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 500px;
    min-height: 400px;
}

/* Title */
h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
    position: relative;
}

h1::after {
    content: '';
    display: block;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    margin: 10px auto;
    border-radius: 2px;
}

/* Add Task Section */
.add-task {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

#task-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

#task-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#task-input::placeholder {
    color: #aaa;
    font-style: italic;
}

#add-button {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

#add-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

#add-button:active {
    transform: translateY(0);
}

/* Task List */
#task-list {
    list-style: none;
    padding: 0;
}

#task-list li {
    background: #f8f9fa;
    margin-bottom: 12px;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

#task-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

#task-list li:hover {
    background: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
}

#task-list li:hover::before {
    transform: scaleY(1);
}

/* Task Text */
#task-list li span,
#task-list li::before {
    font-size: 16px;
    color: #333;
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

/* Delete Button */
.delete-button {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%);
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.delete-button:hover {
    background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

.delete-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* Empty State */
.empty-state {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px 20px;
    font-size: 18px;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        margin: 10px;
        padding: 30px 20px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .add-task {
        flex-direction: column;
        gap: 15px;
    }
    
    #add-button {
        width: 100%;
    }
    
    #task-list li {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .delete-button {
        align-self: flex-end;
        width: fit-content;
    }
}

/* Animation for new tasks */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-enter {
    animation: slideIn 0.3s ease;
}

/* Completed task styling (if you want to add this feature later) */
.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.completed::before {
    background: #4caf50 !important;
}